package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.ModuleRepository;
import com.ascent.solidus.core.dao.ParentModuleRepository;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ascent.solidus.core.domain.module.ModuleConfigDTO;
import java.util.*;

@Service
public class ModuleService {

    @Autowired
    private ModuleRepository moduleRepository;

    @Autowired
    private ParentModuleRepository parentModuleRepository;

    @Autowired
    private ModuleConversionService conversionService;

    public ModuleConfig createModule(ModuleConfig moduleConfig) {
        conversionService.convertParentNameToId(moduleConfig);

        ModuleConfig module = new ModuleConfig();
        // Map DTO to entity
        module.setModuleId(moduleConfig.getModuleId());
        module.setModuleName(moduleConfig.getModuleName());
        module.setParentModuleId(moduleConfig.getParentModuleId());
        // Set other properties

        return moduleRepository.save(module);
    }

    public List<ModuleConfig> getAllModules() {
        return moduleRepository.findAll();
    }

    public ModuleConfig getModuleById(String moduleId) {
        return moduleRepository.findById(moduleId)
                .orElseThrow(() -> new EntityNotFoundException("Module not found"));
    }

    public ModuleConfig updateModule(String moduleId, ModuleConfig moduleConfig) {
        ModuleConfig existing = getModuleById(moduleId);
        conversionService.convertParentNameToId(moduleConfig);

        // Update fields
        existing.setModuleName(moduleConfig.getModuleName());
        existing.setParentModuleId(moduleConfig.getParentModuleId());
        // Update other properties

        return moduleRepository.save(existing);
    }

    public void deleteModule(String moduleId) {
        moduleRepository.deleteById(moduleId);
    }
}