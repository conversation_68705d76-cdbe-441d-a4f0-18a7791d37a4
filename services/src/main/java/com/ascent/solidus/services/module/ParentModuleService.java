package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.DynamicEntityRegistry;
import com.ascent.solidus.core.dao.ParentModuleRepository;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.ascent.solidus.core.domain.module.ParentModule;
import com.ascent.solidus.core.domain.module.ParentModuleDTO;
import com.ascent.solidus.core.domain.module.SubmoduleConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ParentModuleService {

    @Autowired
    private ParentModuleRepository parentModuleRepo;

    @Autowired
    private DynamicEntityRegistry entityRegistry;

    private final Map<String, List<SubmoduleConfig>> submoduleStore = new ConcurrentHashMap<>();


    public ParentModule createParentModule(ParentModuleDTO dto) {
        ParentModule module = new ParentModule();
        module.setParentId(dto.getModuleId());
        module.setParentName(dto.getModuleName());
//        module.setModuleType("parent");
//        module.setModuleIcon(dto.getModuleIcon());
//        module.setIsComponent(dto.getSubmodules() != null && !dto.getSubmodules().isEmpty());
//        ParentModule saved = parentModuleRepo.save(module);

        // Register submodules
        if (dto.getSubmodules() != null) {
            dto.getSubmodules().forEach(sub -> {
                ModuleConfig config = convertToModuleConfig(sub,module);
                try {
                    entityRegistry.registerEntity(config);
                    module.getModules().add(config);
                } catch (IOException | InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return parentModuleRepo.save(module);
//        return saved;
    }

    public List<SubmoduleConfig> getSubmodules(ParentModule parentId) {
        return submoduleStore.getOrDefault(parentId, Collections.emptyList());
    }

    public SubmoduleConfig addSubmodule(ParentModule parentModule, SubmoduleConfig submoduleConfig) throws IOException, ClassNotFoundException, InterruptedException {
        // Validate that parent exists
        ParentModule parent = parentModuleRepo.findById(parentModule.getParentId())
                .orElseThrow(() -> new NoSuchElementException("Parent module not found: " + parentModule));

        // Convert SubmoduleConfig → ModuleConfig and register dynamically
        ModuleConfig moduleConfig = convertToModuleConfig(submoduleConfig, parentModule);
        entityRegistry.registerEntity(moduleConfig);

        // Store in memory
        submoduleStore.computeIfAbsent(parentModule.getParentId(), k -> new ArrayList<>()).add(submoduleConfig);

        return submoduleConfig;
    }

    private ModuleConfig convertToModuleConfig(SubmoduleConfig sub, ParentModule parentModule) {
        ModuleConfig config = new ModuleConfig();
        config.setModuleId(sub.getModuleId());
        config.setModuleName(sub.getModuleName());
        config.setParentModuleId(parentModule.getParentId());
        config.setFields(sub.getFields());
        return config;
    }
}

