package com.ascent.solidus.web.controller.module;

import com.ascent.solidus.core.domain.module.FieldConfig;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.ascent.solidus.services.module.ModuleConversionService;
import com.ascent.solidus.services.module.

import java.net.URI;
import java.util.List;


@RestController
@RequestMapping("/api/modules")
public class ModuleController {

    @Autowired
    private ModuleService moduleService;

    @Autowired
    private ModuleConversionService conversionService;

    @Autowired
    private FieldService fieldService;

    // Create Module
    @PostMapping
    public ResponseEntity<ModuleConfig> createModule(@RequestBody ModuleConfig moduleConfig) {
        ModuleConfig created = moduleService.createModule(moduleConfig);
        conversionService.convertParentIdToName(created);
        return ResponseEntity.created(URI.create("/api/modules/" + created.getModuleId()))
                .body(created);
    }

    // Get All Modules
    @GetMapping
    public List<ModuleConfig> getAllModules() {
        List<ModuleConfig> modules = moduleService.getAllModules();
        modules.forEach(conversionService::convertParentIdToName);
        return modules;
    }

    // Get Single Module
    @GetMapping("/{moduleId}")
    public ModuleConfig getModule(@PathVariable String moduleId) {
        ModuleConfig module = moduleService.getModuleById(moduleId);
        conversionService.convertParentIdToName(module);
        return module;
    }

    // Update Module
    @PutMapping("/{moduleId}")
    public ModuleConfig updateModule(
            @PathVariable String moduleId,
            @RequestBody ModuleConfig moduleConfig) {
        ModuleConfig updated = moduleService.updateModule(moduleId, moduleConfig);
        conversionService.convertParentIdToName(updated);
        return updated;
    }

    // Delete Module
    @DeleteMapping("/{moduleId}")
    public ResponseEntity<Void> deleteModule(@PathVariable String moduleId) {
        moduleService.deleteModule(moduleId);
        return ResponseEntity.noContent().build();
    }

    // Field CRUD Operations

    // Add Field to Module
    @PostMapping("/{moduleId}/fields")
    public FieldConfig addFieldToModule(
            @PathVariable String moduleId,
            @RequestBody FieldConfig fieldConfig) {
        return fieldService.addFieldToModule(moduleId, fieldConfig);
    }

    // Get All Fields for Module
    @GetMapping("/{moduleId}/fields")
    public List<FieldConfig> getModuleFields(@PathVariable String moduleId) {
        return fieldService.getFieldsByModuleId(moduleId);
    }

    // Update Field
    @PutMapping("/{moduleId}/fields/{fieldName}")
    public FieldConfig updateField(
            @PathVariable String moduleId,
            @PathVariable String fieldName,
            @RequestBody FieldConfig fieldConfig) {
        return fieldService.updateField(moduleId, fieldName, fieldConfig);
    }

    // Delete Field
    @DeleteMapping("/{moduleId}/fields/{fieldName}")
    public ResponseEntity<Void> deleteField(
            @PathVariable String moduleId,
            @PathVariable String fieldName) {
        fieldService.deleteField(moduleId, fieldName);
        return ResponseEntity.noContent().build();
    }
}