package com.ascent.solidus.web.controller;


import com.ascent.solidus.core.domain.module.ParentModule;
import com.ascent.solidus.core.domain.module.ParentModuleDTO;
import com.ascent.solidus.core.domain.module.SubmoduleConfig;
import com.ascent.solidus.services.module.ParentModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URI;
import java.util.List;

@RestController
@RequestMapping("/api/parent-modules")
public class ParentModuleController {

    @Autowired
    private ParentModuleService parentModuleService;

    @PostMapping
    public ResponseEntity<ParentModule> createParentModule(
            @RequestBody ParentModuleDTO parentModuleDTO) {
        ParentModule created = parentModuleService.createParentModule(parentModuleDTO);
        return ResponseEntity.created(URI.create("/api/parent-modules/" + created.getParentId()))
                .body(created);
    }

    @GetMapping("/{moduleId}/submodules")
    public ResponseEntity<List<SubmoduleConfig>> getSubmodules(
            @PathVariable ParentModule moduleId) {
        return ResponseEntity.ok(parentModuleService.getSubmodules(moduleId));
    }

    @PostMapping("/{parentId}/submodules")
    public ResponseEntity<SubmoduleConfig> addSubmodule(
            @PathVariable ParentModule parentId,
            @RequestBody SubmoduleConfig submoduleConfig) throws IOException, ClassNotFoundException, InterruptedException {
        SubmoduleConfig created = parentModuleService.addSubmodule(parentId, submoduleConfig);
        return ResponseEntity.created(
                        URI.create("/api/parent-modules/" + parentId + "/submodules/" + created.getModuleId()))
                .body(created);
    }
}
