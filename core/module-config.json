{"parentId": "123", "parentName": "Product_Management", "moduleType": "parent", "modules": [{"moduleId": "Product_001", "moduleName": "Product", "moduleType": "dependent", "dependency": true, "dependencies": ["ProductFamily"], "parentName": "Product_Management", "fields": [{"fieldName": "name", "fieldType": "text", "required": true}, {"fieldName": "drawingNumber", "fieldType": "text", "required": false}, {"fieldName": "path", "fieldType": "text", "required": false}, {"fieldName": "productFamily", "fieldType": "reference", "referenceModule": "ProductFamily", "required": false}, {"fieldName": "auditType", "fieldType": "enum", "enumValues": ["PROCESS_AUDIT", "FIVE_S_AUDIT", "DIB_AUDIT"], "required": true}]}]}