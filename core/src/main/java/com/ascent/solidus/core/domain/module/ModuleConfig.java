package com.ascent.solidus.core.domain.module;

import jakarta.persistence.*;

import java.util.List;

@Entity
@Table(name ="module_config")
public class ModuleConfig {

    @Id
    private String moduleId;
    private String moduleName;
//    private String parentModuleId;


    private String parentName;

//    @Transient
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_module_id", referencedColumnName = "module_id")
    private ParentModule parentModule;

    private List<FieldConfig> fields;
    private String moduleType;
    private List<String> dependencies;
    private Boolean dependency;
    private List<QueryConfig> queryConfigs;

    // Constructors
    public ModuleConfig() {}

    public ModuleConfig(String moduleId, String moduleName,
                        List<FieldConfig> fields, List<QueryConfig> queryConfigs) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
//        this.parentModuleId = parentModuleId;
        this.fields = fields;
        this.queryConfigs = queryConfigs;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    // Getters and Setters
    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public ParentModule getParentModule() {
        if (parentModule == null) {
            parentModule = new ParentModule();
//            parentModule.setParentId(parentModuleId);
        }
        return parentModule;
    }

    public void setParentModule(ParentModule parentModule) {
        this.parentModule = parentModule;
//        this.parentModuleId = parentModule != null ? parentModule.getParentId() : null;
    }

//    public String getParentModuleId() {
//        return parentModuleId != null ? parentModuleId :
//                (parentModule != null ? parentModule.getParentId() : null);
//    }

//    public void setParentModuleId(String parentModuleId) {
////        this.parentModuleId = parentModuleId;
//        if (parentModule == null && parentModuleId != null) {
//            parentModule = new ParentModule();
//            parentModule.setParentId(parentModuleId);
//        }
//    }

    public List<FieldConfig> getFields() {
        return fields;
    }

    public void setFields(List<FieldConfig> fields) {
        this.fields = fields;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public List<String> getDependencies() {
        return dependencies;
    }

    public void setDependencies(List<String> dependencies) {
        this.dependencies = dependencies;
    }

    public Boolean getDependency() {
        return dependency;
    }

    public void setDependency(Boolean dependency) {
        this.dependency = dependency;
    }

    public List<QueryConfig> getQueryConfigs() {
        return queryConfigs;
    }

    public void setQueryConfigs(List<QueryConfig> queryConfigs) {
        this.queryConfigs = queryConfigs;
    }

    // Helper methods
    public boolean isIndependent() {
        return "independent".equalsIgnoreCase(moduleType);
    }

    public boolean isDependent() {
        return "dependent".equalsIgnoreCase(moduleType);
    }

    public boolean isParent() {
        return "parent".equalsIgnoreCase(moduleType);
    }

    public String getParentModuleId() {
        return parentModule != null ? parentModule.getParentId() : null;
    }

    public void setParentModuleId(String parentId) {
        if (parentId == null) {
            this.parentModule = null;
        } else {
            if (this.parentModule == null) {
                this.parentModule = new ParentModule();
            }
            this.parentModule.setParentId(parentId);
        }
    }


    @Override
    public String toString() {
        return "ModuleConfig{" +
                "moduleId='" + moduleId + '\'' +
                ", moduleName='" + moduleName + '\'' +
                ", moduleType='" + moduleType + '\'' +
                ", dependency=" + dependency +
                ", fields=" + fields +
                ", queryConfigs=" + queryConfigs +
                '}';
    }
}