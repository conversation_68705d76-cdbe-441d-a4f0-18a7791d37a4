package com.ascent.solidus.core;

import com.ascent.solidus.core.dao.*;
import com.ascent.solidus.core.domain.module.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.squareup.javapoet.JavaFile;
import org.springframework.beans.factory.annotation.Autowired;

import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.StandardJavaFileManager;
import javax.tools.ToolProvider;
import java.io.*;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.Arrays;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.capitalize;

public class CodeGenRunner {

    private static final String BASE_PACKAGE = "com.ascent.solidus.core.domain.module";

    public static void main(String[] args) throws Exception {
        // 1. Load config
        String jsonConfig = Files.readString(Paths.get("module-config.json"));
        ModuleConfigRoot configRoot = new ObjectMapper().readValue(jsonConfig, ModuleConfigRoot.class);


        // 2. Initialize generators
        DynamicEntityGenerator entityGen = new DynamicEntityGenerator();
        DynamicRepositoryGenerator repoGen = new DynamicRepositoryGenerator();
        DynamicServiceGenerator serviceGen = new DynamicServiceGenerator();
        DynamicControllerGenerator controllerGen = new DynamicControllerGenerator();
        DynamicEnumGenerator enumGen = new DynamicEnumGenerator(); // Add this line

        Path sourceDir = Paths.get("src/main/java");
        Path outputDir = Paths.get("target/classes");
        Files.createDirectories(outputDir);

        // 4. Generate code
        for (ModuleConfig module : configRoot.getModules()) {

            for (FieldConfig field : module.getFields()) {
                if ("enum".equalsIgnoreCase(field.getFieldType())) {
                    enumGen.generateEnumClass(field);
                    compileEnumClass(sourceDir, capitalize(field.getFieldName()));
                }
            }
            // Generate and write entity (already writes to disk)
            JavaFile entityFile = entityGen.generateEntityClass(module);

            // Verify file was written
            Path entityPath = sourceDir.resolve(BASE_PACKAGE.replace('.', '/') + "/" + module.getModuleName() + ".java");
            if (!Files.exists(entityPath)) {
                throw new IOException("Entity file not found at: " + entityPath);
            }

            // Compile and load
            Class<?> entityClass = compileAndLoadClass(sourceDir, module.getModuleName());

            // Generate dependent components
            repoGen.generateRepositoryInterface(entityClass, module);
            serviceGen.generateServiceClass(entityClass, module);
            controllerGen.generateControllerClass(entityClass, module);
        }

        System.out.println("Code generation complete!");
    }

    private String loadJsonConfig() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("module-config.json")) {
            if (is == null) {
                throw new FileNotFoundException("module-config.json not found in classpath");
            }
            return new String(is.readAllBytes(), StandardCharsets.UTF_8);
        }
    }

//    private static Class<?> compileAndLoadClass(Path sourceDir, String className) throws IOException, ClassNotFoundException, InterruptedException {
//        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
//        if (compiler == null) {
//            throw new IllegalStateException("No system Java compiler found. Are you running a JRE instead of a JDK?");
//        }
//
//        // Define paths
//        Path javaFilePath = sourceDir.resolve("com/ascent/solidus/core/domain/module/" + className + ".java");
//        Path outputDir = Paths.get("target/classes/com/ascent/solidus/core/domain/module");
//
//        // Ensure directories exist
//        Files.createDirectories(outputDir);
//
//        StringBuilder classpathBuilder = new StringBuilder("target/classes");
//        try (DirectoryStream<Path> stream = Files.newDirectoryStream(Paths.get("target/dependency"), "*.jar")) {
//            for (Path jar : stream) {
//                classpathBuilder.append(File.pathSeparator).append(jar.toString());
//            }
//        }
//
//        String classpath = classpathBuilder.toString();
//
//
//        // Compile with proper classpath
//        List<String> options = Arrays.asList(
//                "-d", outputDir.toString(),
//                "-classpath", classpath,
//                javaFilePath.toString()
//        );
//
//        int result = compiler.run(null, null, null, options.toArray(new String[0]));
//        Thread.sleep(1000);
//        if (result != 0) {
//            throw new IOException("Compilation failed for: " + javaFilePath);
//        }
//
//        // Use a fresh classloader
//        URLClassLoader classLoader = URLClassLoader.newInstance(
//                new URL[]{outputDir.toUri().toURL()},
//                Thread.currentThread().getContextClassLoader()
//        );
//        // Load the class
//        return Class.forName("com.ascent.solidus.core.domain.module." + className, true, classLoader);
//    }

    private static Class<?> compileAndLoadClass(Path sourceDir, String className) throws Exception {
        // 1. Set up compilation units
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);

        // 2. Prepare source file
        File javaFile = sourceDir.resolve(BASE_PACKAGE.replace('.', '/') + "/" + className + ".java").toFile();
        Iterable<? extends JavaFileObject> compilationUnits = fileManager.getJavaFileObjects(javaFile);

        // 3. Prepare classpath
        String classpath = buildClasspath();

        // 4. Compile
        JavaCompiler.CompilationTask task = compiler.getTask(
                null,
                fileManager,
                null,
                Arrays.asList("-d", "target/classes", "-classpath", classpath),
                null,
                compilationUnits
        );

        if (!task.call()) {
            throw new IOException("Compilation failed for: " + javaFile);
        }

        // 5. Load
        URLClassLoader classLoader = new URLClassLoader(
                new URL[]{new File("target/classes").toURI().toURL()},
                Thread.currentThread().getContextClassLoader()
        );

        return Class.forName(BASE_PACKAGE + "." + className, true, classLoader);
    }

    private static void compileEnumClass(Path sourceDir, String enumName) throws Exception {
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);

        // Prepare source file
        File javaFile = sourceDir.resolve("com/ascent/solidus/core/constants/" + enumName + ".java").toFile();
        Iterable<? extends JavaFileObject> compilationUnits = fileManager.getJavaFileObjects(javaFile);

        // Prepare classpath
        String classpath = buildClasspath();

        // Compile
        JavaCompiler.CompilationTask task = compiler.getTask(
                null,
                fileManager,
                null,
                Arrays.asList("-d", "target/classes", "-classpath", classpath),
                null,
                compilationUnits
        );

        if (!task.call()) {
            throw new IOException("Enum compilation failed for: " + javaFile);
        }
    }

    private static String buildClasspath() throws IOException {
        StringBuilder cp = new StringBuilder();
        cp.append("target/classes");

        // Add all dependency jars
        try (DirectoryStream<Path> ds = Files.newDirectoryStream(Paths.get("target/dependency"), "*.jar")) {
            for (Path jar : ds) {
                cp.append(File.pathSeparator).append(jar.toString());
            }
        }

        // Add hibernate/jpa annotations if needed
        cp.append(File.pathSeparator).append("/path/to/hibernate-core.jar");

        return cp.toString();
    }
}
